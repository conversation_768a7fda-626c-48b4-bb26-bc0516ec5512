package main

import (
	"fmt"
	"os"

	"github.com/airunny/wiki-go-tools/country"
)

func init() {
	// 在导入 country 包之前设置环境变量
	// 模拟问题场景：COUNTRY_FLAG_PATH 设置为文件路径
	os.Setenv("COUNTRY_FLAG_PATH", "./country/countryFlag.json")
}

func main() {
	fmt.Println("=== 测试路径修复功能 ===")
	
	fmt.Println("环境变量设置:")
	fmt.Printf("COUNTRY_FLAG_PATH = %s\n", os.Getenv("COUNTRY_FLAG_PATH"))
	fmt.Printf("COUNTRY_FLAG_FILE_NAME = %s\n", os.Getenv("COUNTRY_FLAG_FILE_NAME"))
	
	// 测试查询功能
	name, url, err := country.GetCountryInfo("zh", "CN")
	if err != nil {
		fmt.Printf("查询失败: %v\n", err)
	} else {
		fmt.Printf("查询成功 - 中国: %s\n", name)
		fmt.Printf("旗帜URL: %s\n", url)
	}
	
	// 测试其他国家
	name2, url2, err2 := country.GetCountryInfo("en", "US")
	if err2 != nil {
		fmt.Printf("美国查询失败: %v\n", err2)
	} else {
		fmt.Printf("美国: %s, URL: %s\n", name2, url2)
	}
	
	// 测试国家代码有效性
	fmt.Printf("CN 是否有效: %v\n", country.IsCountryCodeValid("CN"))
	fmt.Printf("US 是否有效: %v\n", country.IsCountryCodeValid("US"))
	fmt.Printf("DEU 是否有效: %v\n", country.IsCountryCodeValid("DEU"))
	fmt.Printf("XXX 是否有效: %v\n", country.IsCountryCodeValid("XXX"))
}
