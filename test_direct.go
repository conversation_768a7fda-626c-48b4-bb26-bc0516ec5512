package main

import (
	"fmt"
	"os"
)

func main() {
	fmt.Println("=== 直接测试路径处理逻辑 ===")
	
	// 测试路径处理逻辑
	testPaths := []string{
		"./country",                    // 目录路径
		"./country/countryFlag.json",   // 文件路径
		"./nonexistent",                // 不存在的路径
	}
	
	for _, testPath := range testPaths {
		fmt.Printf("\n测试路径: %s\n", testPath)
		
		var filePath string
		
		// 复制 countryFlag.go 中的逻辑
		if info, err := os.Stat(testPath); err == nil && !info.IsDir() {
			// 如果是文件，直接使用
			filePath = testPath
			fmt.Printf("  检测为文件，使用路径: %s\n", filePath)
		} else {
			// 如果是目录，拼接文件名
			flagFileName := "countryFlag.json"
			filePath = testPath + "/" + flagFileName
			fmt.Printf("  检测为目录，拼接后路径: %s\n", filePath)
		}
		
		// 检查最终文件是否存在
		if _, err := os.Stat(filePath); err == nil {
			fmt.Printf("  ✅ 最终文件存在: %s\n", filePath)
		} else {
			fmt.Printf("  ❌ 最终文件不存在: %s (错误: %v)\n", filePath, err)
		}
	}
	
	fmt.Println("\n=== 测试完成 ===")
}
