package main

import (
	"fmt"
	"log"
	"os"

	"github.com/airunny/wiki-go-tools/country"
)

// 在导入country包之前设置环境变量
func init() {
	os.Setenv("COUNTRY_FLAG_PATH", "./country")
	os.Setenv("COUNTRY_FLAG_FILE_NAME", "countryFlag.json")
}

func main() {

	fmt.Println("=== 国家旗帜信息查询示例 ===\n")

	// 手动加载数据（因为环境变量在init之后设置）
	err := country.LoadCountryFlagData("./country/countryFlag.json")
	if err != nil {
		log.Fatalf("加载数据失败: %v", err)
	}

	// 示例1：使用二字码查询
	fmt.Println("1. 使用二字码查询中国信息：")
	countryName, flagURL, err := country.GetCountryInfo("zh", "CN")
	if err != nil {
		log.Printf("查询失败: %v", err)
	} else {
		fmt.Printf("   国家名称: %s\n", countryName)
		fmt.Printf("   旗帜URL: %s\n", flagURL)
	}

	// 示例2：使用三字码查询
	fmt.Println("\n2. 使用三字码查询美国信息：")
	countryName, flagURL, err = country.GetCountryInfo("en", "USA")
	if err != nil {
		log.Printf("查询失败: %v", err)
	} else {
		fmt.Printf("   国家名称: %s\n", countryName)
		fmt.Printf("   旗帜URL: %s\n", flagURL)
	}

	// 示例3：使用数字国家码查询
	fmt.Println("\n3. 使用数字国家码查询日本信息：")
	countryName, flagURL, err = country.GetCountryInfo("ja", "392")
	if err != nil {
		log.Printf("查询失败: %v", err)
	} else {
		fmt.Printf("   国家名称: %s\n", countryName)
		fmt.Printf("   旗帜URL: %s\n", flagURL)
	}

	// 示例4：语言回退机制
	fmt.Println("\n4. 测试语言回退机制（不存在的语言会回退到英语）：")
	countryName, flagURL, err = country.GetCountryInfo("xyz", "DE")
	if err != nil {
		log.Printf("查询失败: %v", err)
	} else {
		fmt.Printf("   国家名称: %s (回退到英语)\n", countryName)
		fmt.Printf("   旗帜URL: %s\n", flagURL)
	}

	// 示例5：仅获取国家名称
	fmt.Println("\n5. 仅获取国家名称：")
	name, err := country.GetCountryName("zh-cn", "FR")
	if err != nil {
		log.Printf("查询失败: %v", err)
	} else {
		fmt.Printf("   法国的中文名称: %s\n", name)
	}

	// 示例6：仅获取旗帜URL
	fmt.Println("\n6. 仅获取旗帜URL：")
	url, err := country.GetFlagURL("UK")
	if err != nil {
		log.Printf("查询失败: %v", err)
	} else {
		fmt.Printf("   英国旗帜URL: %s\n", url)
	}

	// 示例7：检查国家代码有效性
	fmt.Println("\n7. 检查国家代码有效性：")
	codes := []string{"CN", "US", "XXX", "DEU", "999"}
	for _, code := range codes {
		valid := country.IsCountryCodeValid(code)
		fmt.Printf("   %s: %v\n", code, valid)
	}

	// 示例8：获取所有支持的语言
	fmt.Println("\n8. 获取所有支持的语言（前10个）：")
	languages := country.GetAllSupportedLanguages()
	for i, lang := range languages {
		if i >= 10 {
			fmt.Printf("   ... 还有 %d 种语言\n", len(languages)-10)
			break
		}
		fmt.Printf("   %s\n", lang)
	}

	// 示例9：批量查询
	fmt.Println("\n9. 批量查询多个国家：")
	countryCodes := []string{"CN", "US", "JP", "DE", "FR"}
	results := country.GetCountryInfoBatch("en", countryCodes)
	for code, result := range results {
		if result.Error != nil {
			fmt.Printf("   %s: 查询失败 - %v\n", code, result.Error)
		} else {
			fmt.Printf("   %s: %s\n", code, result.CountryName)
		}
	}

	fmt.Println("\n=== 示例结束 ===")
}
