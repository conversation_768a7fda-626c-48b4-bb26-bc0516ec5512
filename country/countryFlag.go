package country

import (
	"encoding/json"
	"errors"
	"os"
	"path"
	"strings"
)

// 全局变量存储国家旗帜数据
var (
	countryFlagData      []countryFlag           // 存储所有国家旗帜数据
	flagCodeMapping      map[string]*countryFlag // 通过国家码快速查找的映射表
	flagTwoCharMapping   map[string]*countryFlag // 通过二字码快速查找的映射表
	flagThreeCharMapping map[string]*countryFlag // 通过三字码快速查找的映射表

	// 错误定义
	ErrCountryNotFound  = errors.New("country not found")
	ErrLanguageNotFound = errors.New("language not found")
	ErrEmptyFlagData    = errors.New("country flag data not loaded")
)

type countryFlag struct {
	TwoCharCode string              `json:"two_char_code"`
	CountryCode string              `json:"country_code"`
	AreaCode    string              `json:"area_code"`
	FlagURL     string              `json:"flag_url"`
	CountryName map[LangCode]string `json:"country_name"`
}

type LangCode string

// init 函数在包初始化时通过环境变量加载国家旗帜数据
func init() {
	var (
		flagPath     = os.Getenv("COUNTRY_FLAG_PATH")      // 国家旗帜数据文件路径环境变量
		flagFileName = os.Getenv("COUNTRY_FLAG_FILE_NAME") // 国家旗帜数据文件名环境变量
		err          error
	)

	// 如果没有设置路径环境变量，则跳过加载
	if flagPath == "" {
		return
	}

	// 如果没有设置文件名，使用默认文件名
	if flagFileName == "" {
		flagFileName = "countryFlag.json"
	}

	// 加载JSON数据文件
	err = loadCountryFlagData(path.Join(flagPath, flagFileName))
	if err != nil {
		panic(err)
	}
}

// LoadCountryFlagData 从指定路径加载国家旗帜JSON数据（公共函数）
func LoadCountryFlagData(filePath string) error {
	return loadCountryFlagData(filePath)
}

// loadCountryFlagData 从指定路径加载国家旗帜JSON数据
func loadCountryFlagData(filePath string) error {
	// 读取JSON文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	// 解析JSON数据到结构体切片
	err = json.Unmarshal(data, &countryFlagData)
	if err != nil {
		return err
	}

	// 初始化映射表以提高查询效率
	initFlagMappings()

	return nil
}

// initFlagMappings 初始化各种查找映射表
func initFlagMappings() {
	flagCodeMapping = make(map[string]*countryFlag, len(countryFlagData))
	flagTwoCharMapping = make(map[string]*countryFlag, len(countryFlagData))
	flagThreeCharMapping = make(map[string]*countryFlag, len(countryFlagData))

	for i := range countryFlagData {
		flag := &countryFlagData[i]

		// 建立国家码映射
		if flag.CountryCode != "" {
			flagCodeMapping[flag.CountryCode] = flag
		}

		// 建立二字码映射（转为大写）
		if flag.TwoCharCode != "" {
			flagTwoCharMapping[strings.ToUpper(flag.TwoCharCode)] = flag
		}

		// 通过现有的country.go映射建立三字码映射
		// 利用threeCharCodeToCountryCodeMapping来支持三字码查询
		if flag.CountryCode != "" {
			for threeChar, countryCode := range threeCharCodeToCountryCodeMapping {
				if countryCode == flag.CountryCode {
					flagThreeCharMapping[threeChar] = flag
				}
			}
		}
	}
}

// GetCountryInfo 根据语言代码和国家代码获取国家名称和旗帜URL
// 支持通过二字码、三字码或数字国家码进行查询
// 如果找不到指定语言，返回英语名称；如果英语也没有，返回空字符串
func GetCountryInfo(langCode LangCode, countryCode string) (countryName string, flagURL string, err error) {
	// 检查数据是否已加载
	if len(countryFlagData) == 0 {
		err = ErrEmptyFlagData
		return
	}

	// 查找国家信息
	flag := findCountryFlag(countryCode)
	if flag == nil {
		err = ErrCountryNotFound
		return
	}

	flagURL = flag.FlagURL

	// 获取指定语言的国家名称
	countryName = getCountryNameByLang(flag, langCode)

	return
}

// findCountryFlag 根据国家代码查找国家旗帜信息
// 支持二字码、三字码和数字国家码
func findCountryFlag(countryCode string) *countryFlag {
	// 去除空格并转为大写
	code := strings.ToUpper(strings.TrimSpace(countryCode))
	if code == "" {
		return nil
	}

	// 首先尝试通过数字国家码查找
	if flag, ok := flagCodeMapping[code]; ok {
		return flag
	}

	// 尝试通过二字码查找
	if flag, ok := flagTwoCharMapping[code]; ok {
		return flag
	}

	// 尝试通过三字码查找
	if flag, ok := flagThreeCharMapping[code]; ok {
		return flag
	}

	return nil
}

// getCountryNameByLang 根据语言代码获取国家名称
// 如果找不到指定语言，返回英语名称；如果英语也没有，返回空字符串
func getCountryNameByLang(flag *countryFlag, langCode LangCode) string {
	if flag == nil || flag.CountryName == nil {
		return ""
	}

	// 尝试获取指定语言的名称
	if name, ok := flag.CountryName[langCode]; ok && name != "" {
		return name
	}

	// 如果指定语言不存在，尝试获取英语名称
	if name, ok := flag.CountryName["en"]; ok && name != "" {
		return name
	}

	// 如果英语也不存在，返回空字符串
	return ""
}

// GetCountryName 仅获取国家名称
func GetCountryName(langCode LangCode, countryCode string) (string, error) {
	countryName, _, err := GetCountryInfo(langCode, countryCode)
	return countryName, err
}

// GetFlagURL 仅获取国家旗帜URL
func GetFlagURL(countryCode string) (string, error) {
	_, flagURL, err := GetCountryInfo("en", countryCode) // 使用英语作为默认语言
	return flagURL, err
}

// IsCountryCodeValid 检查国家代码是否有效
func IsCountryCodeValid(countryCode string) bool {
	return findCountryFlag(countryCode) != nil
}

// GetAllSupportedLanguages 获取所有支持的语言代码
func GetAllSupportedLanguages() []LangCode {
	if len(countryFlagData) == 0 {
		return nil
	}

	langSet := make(map[LangCode]bool)

	// 遍历所有国家数据，收集所有语言代码
	for _, flag := range countryFlagData {
		for langCode := range flag.CountryName {
			langSet[langCode] = true
		}
	}

	// 转换为切片
	languages := make([]LangCode, 0, len(langSet))
	for langCode := range langSet {
		languages = append(languages, langCode)
	}

	return languages
}

// GetCountryInfoBatch 批量获取多个国家的信息
func GetCountryInfoBatch(langCode LangCode, countryCodes []string) map[string]struct {
	CountryName string
	FlagURL     string
	Error       error
} {
	result := make(map[string]struct {
		CountryName string
		FlagURL     string
		Error       error
	}, len(countryCodes))

	for _, code := range countryCodes {
		countryName, flagURL, err := GetCountryInfo(langCode, code)
		result[code] = struct {
			CountryName string
			FlagURL     string
			Error       error
		}{
			CountryName: countryName,
			FlagURL:     flagURL,
			Error:       err,
		}
	}

	return result
}
