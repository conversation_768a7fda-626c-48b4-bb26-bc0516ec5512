package country

type countryFlag struct {
	TwoCharCode   string                   `json:"two_char_code"`
	ThreeCharCode string                   `json:"three_char_code"`
	CountryCode   string                   `json:"country_code"`
	AreaCode      string                   `json:"area_code"`
	FlagURL       string                   `json:"flag_url"`
	CountryName   map[LangCode]countryName `json:"country_name"`
}

type LangCode string

type countryName string


