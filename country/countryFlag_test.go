package country

import (
	"os"
	"testing"
)

// TestGetCountryInfo 测试获取国家信息功能
func TestGetCountryInfo(t *testing.T) {
	// 设置测试环境变量
	os.Setenv("COUNTRY_FLAG_PATH", ".")
	os.Setenv("COUNTRY_FLAG_FILE_NAME", "countryFlag.json")
	
	// 重新加载数据
	err := loadCountryFlagData("countryFlag.json")
	if err != nil {
		t.Ski<PERSON>("跳过测试，无法加载数据文件: %v", err)
		return
	}

	tests := []struct {
		name        string
		langCode    LangCode
		countryCode string
		wantName    string
		wantURL     string
		wantErr     bool
	}{
		{
			name:        "测试中文-中国二字码",
			langCode:    "zh",
			countryCode: "CN",
			wantName:    "中国",
			wantErr:     false,
		},
		{
			name:        "测试英文-美国二字码",
			langCode:    "en",
			countryCode: "US",
			wantName:    "United States",
			wantErr:     false,
		},
		{
			name:        "测试三字码-德国",
			langCode:    "en",
			countryCode: "DEU",
			wantName:    "Germany",
			wantErr:     false,
		},
		{
			name:        "测试数字国家码-日本",
			langCode:    "zh",
			countryCode: "392",
			wantName:    "日本",
			wantErr:     false,
		},
		{
			name:        "测试不存在的国家",
			langCode:    "en",
			countryCode: "XXX",
			wantErr:     true,
		},
		{
			name:        "测试不存在的语言回退到英语",
			langCode:    "xyz",
			countryCode: "US",
			wantName:    "United States",
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			countryName, flagURL, err := GetCountryInfo(tt.langCode, tt.countryCode)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCountryInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			
			if !tt.wantErr {
				if tt.wantName != "" && countryName != tt.wantName {
					t.Errorf("GetCountryInfo() countryName = %v, want %v", countryName, tt.wantName)
				}
				
				if flagURL == "" {
					t.Errorf("GetCountryInfo() flagURL should not be empty")
				}
				
				t.Logf("国家: %s, 旗帜URL: %s", countryName, flagURL)
			}
		})
	}
}

// TestGetCountryName 测试仅获取国家名称
func TestGetCountryName(t *testing.T) {
	// 设置测试环境变量
	os.Setenv("COUNTRY_FLAG_PATH", ".")
	
	// 重新加载数据
	err := loadCountryFlagData("countryFlag.json")
	if err != nil {
		t.Skipf("跳过测试，无法加载数据文件: %v", err)
		return
	}

	name, err := GetCountryName("zh", "CN")
	if err != nil {
		t.Errorf("GetCountryName() error = %v", err)
		return
	}
	
	t.Logf("中国的中文名称: %s", name)
}

// TestGetFlagURL 测试仅获取旗帜URL
func TestGetFlagURL(t *testing.T) {
	// 设置测试环境变量
	os.Setenv("COUNTRY_FLAG_PATH", ".")
	
	// 重新加载数据
	err := loadCountryFlagData("countryFlag.json")
	if err != nil {
		t.Skipf("跳过测试，无法加载数据文件: %v", err)
		return
	}

	url, err := GetFlagURL("CN")
	if err != nil {
		t.Errorf("GetFlagURL() error = %v", err)
		return
	}
	
	if url == "" {
		t.Errorf("GetFlagURL() should return non-empty URL")
	}
	
	t.Logf("中国旗帜URL: %s", url)
}

// TestIsCountryCodeValid 测试国家代码有效性检查
func TestIsCountryCodeValid(t *testing.T) {
	// 设置测试环境变量
	os.Setenv("COUNTRY_FLAG_PATH", ".")
	
	// 重新加载数据
	err := loadCountryFlagData("countryFlag.json")
	if err != nil {
		t.Skipf("跳过测试，无法加载数据文件: %v", err)
		return
	}

	tests := []struct {
		code  string
		valid bool
	}{
		{"CN", true},
		{"US", true},
		{"XXX", false},
		{"", false},
	}

	for _, tt := range tests {
		if IsCountryCodeValid(tt.code) != tt.valid {
			t.Errorf("IsCountryCodeValid(%s) = %v, want %v", tt.code, !tt.valid, tt.valid)
		}
	}
}

// TestGetAllSupportedLanguages 测试获取所有支持的语言
func TestGetAllSupportedLanguages(t *testing.T) {
	// 设置测试环境变量
	os.Setenv("COUNTRY_FLAG_PATH", ".")
	
	// 重新加载数据
	err := loadCountryFlagData("countryFlag.json")
	if err != nil {
		t.Skipf("跳过测试，无法加载数据文件: %v", err)
		return
	}

	languages := GetAllSupportedLanguages()
	if len(languages) == 0 {
		t.Errorf("GetAllSupportedLanguages() should return non-empty slice")
	}
	
	t.Logf("支持的语言数量: %d", len(languages))
	for _, lang := range languages {
		t.Logf("支持的语言: %s", lang)
	}
}
